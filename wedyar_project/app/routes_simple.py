from flask import Blueprint, render_template, request, redirect, url_for, flash, session

main = Blueprint("main", __name__)

@main.route("/")
def home():
    """الصفحة الرئيسية"""
    return render_template("home_simple.html")

@main.route("/set_language/<language>")
def set_language(language):
    """تغيير اللغة"""
    if language in ['ar', 'en']:
        session['language'] = language
    return redirect(request.referrer or url_for('main.home'))

@main.route("/contact", methods=["GET", "POST"])
def contact():
    """صفحة الاتصال"""
    if request.method == "POST":
        name = request.form.get("name")
        email = request.form.get("email")
        phone = request.form.get("phone")
        subject = request.form.get("subject")
        message = request.form.get("message")
        
        # هنا يمكن إضافة منطق إرسال الإيميل لاحقاً
        flash("تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.", "success")
        return redirect(url_for("main.home") + "#contact")
    
    return render_template("home_simple.html")

# Routes للصفحات الأخرى (للتوافق مع الروابط الموجودة)
@main.route("/services")
def services():
    return redirect(url_for("main.home") + "#services")

@main.route("/projects")
def projects():
    return redirect(url_for("main.home") + "#projects")

@main.route("/team")
def team():
    return redirect(url_for("main.home") + "#team")

@main.route("/about")
def about():
    return redirect(url_for("main.home") + "#about")
