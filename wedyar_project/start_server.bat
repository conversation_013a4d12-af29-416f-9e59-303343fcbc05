@echo off
chcp 65001 >nul
title MYTHAQ Engineering Consultancy Server
color 0A

echo ============================================================
echo                MYTHAQ Engineering Consultancy
echo ============================================================
echo.

echo [INFO] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

echo [INFO] Python found successfully
echo.

echo [INFO] Installing requirements...
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Some packages may not be installed, but continuing...
)

echo.
echo [INFO] Starting MYTHAQ server...
echo [INFO] Server will be available at: http://127.0.0.1:5000
echo [INFO] Press Ctrl+C to stop the server
echo.
echo [DEBUG] If the server doesn't start, try running: python test_server.py
echo.

python run.py
if errorlevel 1 (
    echo.
    echo [ERROR] Main server failed to start. Trying test server...
    echo.
    python test_server.py
)

echo.
echo [INFO] Server stopped
pause
