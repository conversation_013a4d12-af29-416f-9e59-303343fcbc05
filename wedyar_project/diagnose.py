#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import traceback

def check_python():
    print("🐍 فحص Python...")
    print(f"   الإصدار: {sys.version}")
    print(f"   المسار: {sys.executable}")
    return True

def check_flask():
    print("\n🌶️ فحص Flask...")
    try:
        import flask
        print(f"   ✅ Flask مثبت - الإصدار: {flask.__version__}")
        return True
    except ImportError as e:
        print(f"   ❌ Flask غير مثبت: {e}")
        return False

def check_bootstrap():
    print("\n🎨 فحص Flask-Bootstrap...")
    try:
        import flask_bootstrap
        print(f"   ✅ Flask-Bootstrap مثبت")
        return True
    except ImportError as e:
        print(f"   ❌ Flask-Bootstrap غير مثبت: {e}")
        return False

def check_files():
    print("\n📁 فحص الملفات...")
    required_files = [
        'run.py',
        'app/__init__.py',
        'app/routes.py',
        'app/templates/base.html',
        'app/templates/home.html',
        'app/static/css/style.css'
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} ({size} bytes)")
            if size > 50000:  # أكبر من 50KB
                print(f"      ⚠️  الملف كبير جداً! قد يسبب مشاكل")
        else:
            print(f"   ❌ {file} غير موجود")
            all_exist = False
    
    return all_exist

def test_import():
    print("\n🔍 اختبار استيراد الملفات...")
    try:
        sys.path.insert(0, os.getcwd())
        from app import create_app
        print("   ✅ تم استيراد create_app بنجاح")
        
        app = create_app()
        print("   ✅ تم إنشاء التطبيق بنجاح")
        return True
    except Exception as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        print("   📋 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

def test_simple_server():
    print("\n🧪 اختبار خادم بسيط...")
    try:
        from flask import Flask
        app = Flask(__name__)
        
        @app.route('/')
        def test():
            return "✅ الخادم يعمل!"
        
        print("   ✅ تم إنشاء خادم اختبار بسيط")
        print("   💡 يمكنك تشغيله بالأمر: python test_server.py")
        return True
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء الخادم: {e}")
        return False

def main():
    print("🔧 أداة تشخيص مشاكل MYTHAQ")
    print("=" * 50)
    
    checks = [
        check_python(),
        check_flask(),
        check_bootstrap(),
        check_files(),
        test_import(),
        test_simple_server()
    ]
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    
    passed = sum(checks)
    total = len(checks)
    
    print(f"   ✅ نجح: {passed}/{total}")
    print(f"   ❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 جميع الفحوصات نجحت! المشكلة قد تكون في:")
        print("   • حجم ملف home.html الكبير (53KB)")
        print("   • تعقيد HTML/CSS")
        print("   • مشكلة في المتصفح أو التخزين المؤقت")
        print("\n💡 الحلول المقترحة:")
        print("   1. استخدم python test_server.py للاختبار")
        print("   2. قسم الملف إلى أجزاء أصغر")
        print("   3. امسح التخزين المؤقت للمتصفح")
    else:
        print("\n⚠️  يوجد مشاكل تحتاج إلى حل أولاً")
        print("💡 قم بتثبيت المكتبات المفقودة:")
        print("   pip install flask flask-bootstrap4")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
